# C/C++ build system timings
generate_cxx_metadata
  [gap of 60ms]
  create-invalidation-state 298ms
  [gap of 242ms]
  write-metadata-json-to-file 44ms
  [gap of 46ms]
generate_cxx_metadata completed in 690ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 309ms]
  create-invalidation-state 880ms
  [gap of 345ms]
  write-metadata-json-to-file 50ms
  [gap of 37ms]
generate_cxx_metadata completed in 1621ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 148ms]
  create-invalidation-state 1002ms
  [gap of 258ms]
  write-metadata-json-to-file 31ms
  [gap of 102ms]
generate_cxx_metadata completed in 1541ms

