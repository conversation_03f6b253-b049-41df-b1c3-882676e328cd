-- Fix call_transactions foreign key constraint
-- This script fixes the foreign key constraint violation where call_transactions.call_id
-- references user_video_calls.call_id but VideoSDK calls are stored in user_calls table
--
-- Background:
-- - VideoSDK calls (both voice and video) use user_calls table
-- - Legacy video calls use user_video_calls table
-- - call_transactions table should reference user_calls for VideoSDK system

-- Step 1: Check current system state
SELECT 'Current foreign key constraints:' as info;
SELECT
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'call_transactions'
  AND CONSTRAINT_NAME LIKE '%ibfk%';

-- Step 2: Check if there are any existing call_transactions records
SELECT 'Existing call_transactions count:' as info;
SELECT COUNT(*) as total_transactions FROM call_transactions;

-- Step 3: Check if any existing transactions would violate the new constraint
SELECT 'Checking for orphaned transactions:' as info;
SELECT COUNT(*) as orphaned_count
FROM call_transactions ct
LEFT JOIN user_calls uc ON ct.call_id = uc.call_id
WHERE uc.call_id IS NULL;

-- Step 4: Show any orphaned transactions (if any)
SELECT 'Orphaned transactions (if any):' as info;
SELECT ct.id, ct.call_id, ct.call_type, ct.created_at
FROM call_transactions ct
LEFT JOIN user_calls uc ON ct.call_id = uc.call_id
WHERE uc.call_id IS NULL
LIMIT 10;

-- Step 5: Drop the existing foreign key constraint that references user_video_calls
ALTER TABLE call_transactions
DROP FOREIGN KEY call_transactions_ibfk_3;

-- Step 6: Add new foreign key constraint that references user_calls table instead
-- This is where VideoSDK calls are actually stored
ALTER TABLE call_transactions
ADD CONSTRAINT call_transactions_ibfk_3
FOREIGN KEY (call_id) REFERENCES user_calls(call_id)
ON DELETE CASCADE;

-- Step 7: Verify the new constraint
SELECT 'New foreign key constraint:' as info;
SELECT
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'call_transactions'
  AND CONSTRAINT_NAME = 'call_transactions_ibfk_3';

-- Step 8: Display success message
SELECT 'call_transactions foreign key constraint fixed successfully!' as message;
SELECT 'VideoSDK calls can now create transactions without foreign key violations.' as note;
