const moment = require('moment');
const { queryRunner } = require('../dbConfig/queryRunner');

class VideoSDKCallService {
  // Get user's subscription status and determine call rates
  static async getUserSubscriptionStatus(userId) {
    try {
      const query = `
        SELECT 
          s.status, 
          s.current_end_at,
          s.razorpay_plan_id,
          p.name as plan_name,
          p.amount,
          u.is_premium,
          u.premium_expires_at
        FROM user_subscriptions s
        LEFT JOIN razorpay_plans p ON s.razorpay_plan_id = p.id
        LEFT JOIN users u ON s.user_id = u.id
        WHERE s.user_id = ? AND s.status = 'active'
        ORDER BY s.created_at DESC
        LIMIT 1
      `;
      
      const result = await queryRunner(query, [userId]);
      
      if (result.length > 0) {
        const subscription = result[0];
        const isActive = subscription.status === 'active' && 
                        subscription.current_end_at && 
                        new Date(subscription.current_end_at) > new Date();
        
        return {
          hasActiveSubscription: isActive,
          planName: subscription.plan_name || 'No Plan',
          amount: subscription.amount || 0,
          isPremium: subscription.is_premium || false
        };
      }
      
      return {
        hasActiveSubscription: false,
        planName: 'No Plan',
        amount: 0,
        isPremium: false
      };
    } catch (error) {
      console.error("Error getting user subscription status:", error);
      throw error;
    }
  }

  // Get user's wallet balance
  static async getUserWalletBalance(userId) {
    try {
      const query = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY createddate DESC LIMIT 1`;
      const result = await queryRunner(query, [userId]);
      return result.length > 0 ? parseFloat(result[0].totalBalance) || 0 : 0;
    } catch (error) {
      console.error("Error getting user wallet balance:", error);
      throw error;
    }
  }

  // Start a VideoSDK call
  static async startVideoSDKCall(callerId, receiverId) {
    try {
      const startTime = moment().utcOffset("+05:30").format("YYYY-MM-DD HH:mm:ss");

      // Check if users exist
      const callerCheckQuery = `SELECT id, name FROM users WHERE id = ?`;
      const [callerExists] = await queryRunner(callerCheckQuery, [callerId]);

      const receiverCheckQuery = `SELECT id, name, dnd FROM users WHERE id = ?`;
      const [receiverExists] = await queryRunner(receiverCheckQuery, [receiverId]);

      if (!callerExists) {
        return { status: false, statusCode: 400, message: "Caller does not exist." };
      }
      if (!receiverExists) {
        return { status: false, statusCode: 400, message: "Receiver does not exist." };
      }

      // Check if users are blocked
      const blockedCheckQuery = `
        SELECT * FROM blocked_users 
        WHERE (user_id = ? AND blocked_id = ? AND is_blocked = 1) 
        OR (user_id = ? AND blocked_id = ? AND is_blocked = 1)
      `;
      const blockedResult = await queryRunner(blockedCheckQuery, [callerId, receiverId, receiverId, callerId]);
      if (blockedResult.length > 0) {
        return { status: false, statusCode: 400, message: "Video call not allowed due to blocking." };
      }

      // Check DND status
      if (receiverExists.dnd === 1) {
        return { status: false, statusCode: 400, message: "User has enabled DND" };
      }

      // Get caller's subscription status and wallet balance
      const callerSubscription = await this.getUserSubscriptionStatus(callerId);
      const callerBalance = await this.getUserWalletBalance(callerId);

      // Determine caller charge rate based on subscription status
      const callerChargePerMinute = callerSubscription.hasActiveSubscription ? 4 : 7; // 4 INR/min for premium, 7 INR/min for non-premium

      // Calculate maximum call duration based on balance
      const maxCallLimitMinutes = Math.floor(callerBalance / callerChargePerMinute);
      const maxCallLimitSeconds = maxCallLimitMinutes * 60;
      const maxCallLimitDateTime = moment(startTime)
        .add(maxCallLimitMinutes, "minutes")
        .utcOffset("+05:30")
        .format("YYYY-MM-DD HH:mm:ss");

      // Insert call record in user_calls table (consistent with consolidated API)
      const insertCallQuery = `
        INSERT INTO user_calls (caller_user_id, receiver_user_id, start_time, max_call_limit_time, duration_seconds, created_at, call_type, call_status)
        VALUES (?, ?, ?, ?, ?, ?, 'voice-call', 'active')
      `;
      const params = [callerId, receiverId, startTime, maxCallLimitDateTime, maxCallLimitSeconds, startTime];
      const callResult = await queryRunner(insertCallQuery, params);

      return {
        status: true,
        statusCode: 200,
        is_call_ended: false,
        startTime: startTime,
        maxCallLimitTime: maxCallLimitMinutes,
        maxCallLimitDateTime: maxCallLimitDateTime,
        callId: callResult.insertId,
        duration_seconds: maxCallLimitSeconds,
        caller_charge_per_minute: callerChargePerMinute,
        caller_balance: callerBalance,
        caller_subscription_status: callerSubscription,
        message: "VideoSDK call started successfully",
      };
    } catch (error) {
      console.error("Error in startVideoSDKCall:", error);
      return { status: false, statusCode: 400, message: error.message || "Server Error" };
    }
  }

  // End a VideoSDK call
  static async endVideoSDKCall(callerId, receiverId, callId) {
    try {
      let endDateTime = moment().utcOffset("+05:30").format("YYYY-MM-DD HH:mm:ss");

      // Get call details from the correct table (user_calls, not user_video_calls)
      const callDetailsQuery = `SELECT start_time, end_time, call_type FROM user_calls WHERE call_id = ?`;
      const [callData] = await queryRunner(callDetailsQuery, [callId]);
      if (!callData) {
        return { status: false, statusCode: 400, message: "VideoSDK call record not found." };
      }

      const { start_time: startTime, end_time: endTime } = callData;
      let callDurationSeconds = moment(endDateTime).diff(moment(startTime), "seconds");
      
      if (!endTime) {
        const updateEndTimeQuery = `UPDATE user_calls SET end_time = ?, duration = ? WHERE call_id = ?`;
        await queryRunner(updateEndTimeQuery, [endDateTime, callDurationSeconds, callId]);
      }

      if (callDurationSeconds <= 0) {
        return { status: false, statusCode: 400, message: "Invalid video call duration" };
      }

      const callDurationMinutes = callDurationSeconds / 60;

      // Get user details and subscription status
      const callerQuery = `SELECT id AS caller_user_id, name AS caller_user_name FROM users WHERE id = ?`;
      const [callerData] = await queryRunner(callerQuery, [callerId]);

      const receiverQuery = `SELECT id AS receiver_user_id, name AS receiver_user_name FROM users WHERE id = ?`;
      const [receiverData] = await queryRunner(receiverQuery, [receiverId]);

      const callerSubscription = await this.getUserSubscriptionStatus(callerId);
      const receiverSubscription = await this.getUserSubscriptionStatus(receiverId);

      // Calculate charges and earnings based on subscription status
      const callerChargePerMinute = callerSubscription.hasActiveSubscription ? 4 : 7;
      const receiverEarningsPerMinute = receiverSubscription.hasActiveSubscription ? 2 : 0.6;

      const totalCharge = (callDurationMinutes * callerChargePerMinute).toFixed(2);
      const totalEarnings = (callDurationMinutes * receiverEarningsPerMinute).toFixed(2);

      // Get current balances
      const callerBalance = await this.getUserWalletBalance(callerId);
      const receiverBalance = await this.getUserWalletBalance(receiverId);

      const newCallerBalance = (callerBalance - parseFloat(totalCharge)).toFixed(2);
      const newReceiverBalance = (receiverBalance + parseFloat(totalEarnings)).toFixed(2);

      if (newCallerBalance < 0) {
        return { status: false, statusCode: 400, message: "Insufficient balance to complete the video call." };
      }

      // Insert transaction record
      const insertTransactionQuery = `
        INSERT INTO call_transactions (caller_id, receiver_id, call_id, duration_in_minutes, charge_amount, status, created_at, call_type)
        VALUES (?, ?, ?, ?, ?, 'completed', ?, ?)
      `;
      await queryRunner(insertTransactionQuery, [callerId, receiverId, callId, callDurationMinutes.toFixed(2), totalCharge, endDateTime, callData.call_type || 'voice-call']);

      // Update caller's wallet (debit)
      const callTypeForWallet = callData.call_type === 'video-call' ? 'video_call_withdrawal' : 'voice_call_withdrawal';
      const insertCallerDebitQuery = `
        INSERT INTO wallet (amount, transaction_type, createdby, paid_status, call_transaction_id, createddate, totalBalance)
        VALUES (?, ?, ?, 'completed', ?, ?, ?)
      `;
      await queryRunner(insertCallerDebitQuery, [totalCharge, callTypeForWallet, callerId, callId, endDateTime, newCallerBalance]);

      // Update receiver's wallet (credit)
      const callTypeForWalletCredit = callData.call_type === 'video-call' ? 'video_call_earning' : 'voice_call_earning';
      const insertReceiverCreditQuery = `
        INSERT INTO wallet (amount, transaction_type, createdby, paid_status, call_transaction_id, createddate, totalBalance)
        VALUES (?, ?, ?, 'completed', ?, ?, ?)
      `;
      await queryRunner(insertReceiverCreditQuery, [totalEarnings, callTypeForWalletCredit, receiverId, callId, endDateTime, newReceiverBalance]);

      return {
        status: true,
        statusCode: 200,
        caller_user_id: callerData.caller_user_id,
        caller_user_name: callerData.caller_user_name,
        receiver_user_id: receiverData.receiver_user_id,
        receiver_user_name: receiverData.receiver_user_name,
        caller_debited_charge: totalCharge,
        receiver_credited_charge: totalEarnings,
        total_duration_seconds: callDurationSeconds,
        available_caller_balance: newCallerBalance,
        available_receiver_balance: newReceiverBalance,
        caller_subscription_status: callerSubscription,
        receiver_subscription_status: receiverSubscription,
        message: "VideoSDK call ended, transactions recorded successfully",
        is_call_ended: true,
      };
    } catch (error) {
      console.error("Error in endVideoSDKCall:", error);
      return { status: false, statusCode: 400, message: error.message || "Server Error" };
    }
  }

  // Handle missed VideoSDK call
  static async missedVideoSDKCall(callerId, receiverId) {
    try {
      const currentTime = moment().utcOffset("+05:30").format("YYYY-MM-DD HH:mm:ss");

      // Insert missed call record in user_calls table (consistent with consolidated API)
      const insertMissedCallQuery = `
        INSERT INTO user_calls (caller_user_id, receiver_user_id, start_time, end_time, duration, created_at, call_type, call_status)
        VALUES (?, ?, ?, ?, 0, ?, 'voice-call', 'missed')
      `;
      await queryRunner(insertMissedCallQuery, [callerId, receiverId, currentTime, currentTime, currentTime]);

      return {
        status: true,
        statusCode: 200,
        message: "Missed VideoSDK call recorded successfully",
      };
    } catch (error) {
      console.error("Error in missedVideoSDKCall:", error);
      return { status: false, statusCode: 400, message: error.message || "Server Error" };
    }
  }

  // Get user's call balance and subscription status
  static async getCallBalance(userId) {
    try {
      const subscription = await this.getUserSubscriptionStatus(userId);
      const balance = await this.getUserWalletBalance(userId);
      const chargePerMinute = subscription.hasActiveSubscription ? 7 : 12;
      const maxCallMinutes = Math.floor(balance / chargePerMinute);

      return {
        status: true,
        statusCode: 200,
        data: {
          wallet_balance: balance,
          subscription_status: subscription,
          charge_per_minute: chargePerMinute,
          max_call_minutes: maxCallMinutes,
          has_active_subscription: subscription.hasActiveSubscription
        }
      };
    } catch (error) {
      console.error("Error in getCallBalance:", error);
      return { status: false, statusCode: 400, message: error.message || "Server Error" };
    }
  }

  // Get user's call history
  static async getCallHistory(userId, page = 1, limit = 10) {
    try {
      const offset = (page - 1) * limit;
      
      const query = `
        SELECT
          vc.call_id,
          vc.caller_user_id,
          vc.receiver_user_id,
          vc.start_time,
          vc.end_time,
          vc.duration,
          vc.call_status as status,
          vc.call_type,
          caller.name as caller_name,
          receiver.name as receiver_name
        FROM user_calls vc
        LEFT JOIN users caller ON vc.caller_user_id = caller.id
        LEFT JOIN users receiver ON vc.receiver_user_id = receiver.id
        WHERE (vc.caller_user_id = ? OR vc.receiver_user_id = ?) AND vc.call_type = 'voice-call'
        ORDER BY vc.created_at DESC
        LIMIT ? OFFSET ?
      `;
      
      const calls = await queryRunner(query, [userId, userId, limit, offset]);
      
      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM user_calls
        WHERE (caller_user_id = ? OR receiver_user_id = ?) AND call_type = 'voice-call'
      `;
      const [{ total }] = await queryRunner(countQuery, [userId, userId]);

      return {
        status: true,
        statusCode: 200,
        data: {
          calls: calls,
          pagination: {
            current_page: page,
            total_pages: Math.ceil(total / limit),
            total_records: total,
            limit: limit
          }
        }
      };
    } catch (error) {
      console.error("Error in getCallHistory:", error);
      return { status: false, statusCode: 400, message: error.message || "Server Error" };
    }
  }
}

module.exports = VideoSDKCallService; 