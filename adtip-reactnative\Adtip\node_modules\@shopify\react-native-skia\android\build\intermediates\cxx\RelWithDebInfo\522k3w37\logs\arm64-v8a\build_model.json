{"info": {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, "cxxBuildFolder": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\.cxx\\RelWithDebInfo\\522k3w37\\arm64-v8a", "soFolder": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\intermediates\\cxx\\RelWithDebInfo\\522k3w37\\obj\\arm64-v8a", "soRepublishFolder": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\intermediates\\cmake\\release\\obj\\arm64-v8a", "abiPlatformVersion": 24, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DANDROID_STL=c++_shared", "-DREACT_NATIVE_VERSION=79", "-DNODE_MODULES_DIR=F:\\A1\\adtip-reactnative\\Adtip\\node_modules", "-DPREBUILT_DIR=F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build/react-native-0*/jni"], "cFlagsList": [], "cppFlagsList": ["-fexceptions", "-frtti", "-std=c++1y", "-DONANDROID"], "variantName": "release", "isDebuggableEnabled": false, "validAbiList": ["armeabi-v7a", "arm64-v8a"], "buildTargetSet": [], "implicitBuildTargetSet": ["rnskia"], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\.cxx", "intermediatesBaseFolder": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\intermediates", "intermediatesFolder": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\intermediates\\cxx", "gradleModulePathName": ":shopify_react-native-skia", "moduleRootFolder": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android", "moduleBuildFile": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build.gradle", "makeFile": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006", "ndkFolderBeforeSymLinking": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006", "ndkVersion": "27.1.12297006", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "F:\\R17DevTools\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "riscv64": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\riscv64-linux-android\\libc++_shared.so", "x86": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "F:\\A1\\adtip-reactnative\\Adtip\\android", "sdkFolder": "F:\\R17DevTools\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "F:\\R17DevTools\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": ["F:\\R17DevTools\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar"], "prefabPackages": ["F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0dbfeb2f307611649a8567893eebd290\\transformed\\jetified-react-android-0.79.2-release\\prefab", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8d81b0c8ae21d76d183ae0c44210c625\\transformed\\jetified-fbjni-0.7.0\\prefab"], "prefabPackageConfigurations": [], "stlType": "c++_shared", "optimizationTag": "RelWithDebInfo"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\.cxx\\RelWithDebInfo\\522k3w37\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "522k3w372v204y48yf3k2t4cu3j593o632v3e452s4r6v4h6r4p4c3u2w3s6x", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.8.2.\n#   - $NDK is the path to NDK 27.1.12297006.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=24\n-DANDROID_PLATFORM=android-24\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMA<PERSON>_MAKE_PROGRAM=$NINJA\n-DCMAKE_CXX_FLAGS=-fexceptions -frtti -std=c++1y -DONANDROID\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=RelWithDebInfo\n-DCMAKE_FIND_ROOT_PATH=F:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/.cxx/RelWithDebInfo/$HASH/prefab/$ABI/prefab\n-BF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/.cxx/RelWithDebInfo/$HASH/$ABI\n-GNinja\n-DANDROID_STL=c++_shared\n-DREACT_NATIVE_VERSION=79\n-DNODE_MODULES_DIR=F:/A1/adtip-reactnative/Adtip/node_modules\n-DPREBUILT_DIR=F:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/react-native-0*/jni", "configurationArguments": ["-HF:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=24", "-DANDROID_PLATFORM=android-24", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006", "-DCMAKE_ANDROID_NDK=F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006", "-DCMAKE_TOOLCHAIN_FILE=F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=F:\\R17DevTools\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_CXX_FLAGS=-fexceptions -frtti -std=c++1y -DONANDROID", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\intermediates\\cxx\\RelWithDebInfo\\522k3w37\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\intermediates\\cxx\\RelWithDebInfo\\522k3w37\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=RelWithDebInfo", "-DCMAKE_FIND_ROOT_PATH=F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\.cxx\\RelWithDebInfo\\522k3w37\\prefab\\arm64-v8a\\prefab", "-BF:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\.cxx\\RelWithDebInfo\\522k3w37\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>", "-DANDROID_STL=c++_shared", "-DREACT_NATIVE_VERSION=79", "-DNODE_MODULES_DIR=F:\\A1\\adtip-reactnative\\Adtip\\node_modules", "-DPREBUILT_DIR=F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build/react-native-0*/jni"], "stlLibraryFile": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "intermediatesParentFolder": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\intermediates\\cxx\\RelWithDebInfo\\522k3w37"}