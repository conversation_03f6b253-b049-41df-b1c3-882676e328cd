const express = require("express");
const router = express.Router();
const { v4: uuidv4 } = require("uuid");
const admin = require("firebase-admin");

const serviceAccount = require("../serviceAccountKey.json");

// Initialize only once
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
}

// @route   POST /api/call/initiate-call
// @desc    Initiate a call (send FCM)
router.post("/initiate-call", async (req, res) => {
  const { calleeInfo, callerInfo, videoSDKInfo } = req.body;

  // Validate request body
  if (!calleeInfo || !calleeInfo.token || !callerInfo || !videoSDKInfo) {
    return res.status(400).json({error: "Missing required fields"});
  }

  // Use standardized info field format (consistent with functions/routes)
  const info = JSON.stringify({
    callerInfo,
    videoSDKInfo,
    type: "CALL_INITIATED",
    uuid: uuidv4(),
  });

  const message = {
    data: {
      info,
    },
    token: calleeInfo.token,
  };

  // Platform-specific configuration
  if (calleeInfo.platform === "ANDROID") {
    message.android = {
      priority: "high",
    };
  } else if (calleeInfo.platform === "IOS") {
    message.apns = {
      headers: {
        "apns-priority": "10",
      },
      payload: {
        aps: {
          badge: 1,
          sound: "default",
        },
      },
    };
  } else {
    return res.status(400).json({error: "Unsupported platform"});
  }

  try {
    // Use send() instead of sendToDevice() for consistency with functions/routes
    const response = await admin.messaging().send(message);
    res.status(200).json({messageId: response});
  } catch (err) {
    console.error("FCM Error:", err);
    if (err.code === "messaging/invalid-registration-token" ||
        err.code === "messaging/registration-token-not-registered") {
      return res.status(400).json({error: "Invalid or unregistered FCM token"});
    }
    res.status(500).json({ error: err.message });
  }
});

// @route   POST /api/call/update-call
// @desc    Update call status (send FCM)
router.post("/update-call", async (req, res) => {
  const { callerInfo, type } = req.body;

  // Validate request body
  if (!callerInfo || !callerInfo.token || !type) {
    return res.status(400).json({error: "Missing required fields"});
  }

  // Use standardized info field format (consistent with functions/routes)
  const info = JSON.stringify({
    callerInfo,
    type,
  });

  const message = {
    data: {
      info,
    },
    token: callerInfo.token,
  };

  // Platform-specific configuration
  if (callerInfo.platform === "IOS") {
    message.apns = {
      headers: {
        "apns-priority": "10",
      },
      payload: {
        aps: {
          badge: 1,
          sound: "default",
        },
      },
    };
  } else if (callerInfo.platform === "ANDROID") {
    message.android = {
      priority: "high",
    };
  } else {
    return res.status(400).json({error: "Unsupported platform"});
  }

  try {
    // Use send() instead of sendToDevice() for consistency with functions/routes
    const response = await admin.messaging().send(message);
    res.status(200).json({messageId: response});
  } catch (err) {
    console.error("FCM Error:", err);
    if (err.code === "messaging/invalid-registration-token" ||
        err.code === "messaging/registration-token-not-registered") {
      return res.status(400).json({error: "Invalid or unregistered FCM token"});
    }
    res.status(500).json({ error: err.message });
  }
});

module.exports = router;
