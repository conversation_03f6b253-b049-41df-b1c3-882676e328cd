const express = require("express");
const router = express.Router();
const { v4: uuidv4 } = require("uuid");
const admin = require("firebase-admin");
const VideoSDKService = require('../services/videosdk_service');
const { queryRunner } = require('../dbConfig/queryRunner');
const Auth = require('../config/auth');

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  const serviceAccount = require("../config/adtip-3873c-firebase-adminsdk.json");
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
  console.log("[new_initiate_call] Firebase Admin SDK initialized successfully");
} else {
  console.log("[new_initiate_call] Firebase Admin SDK already initialized");
}

/**
 * @route   POST /api/initiate-call
 * @desc    Consolidated API for complete outgoing call flow
 * @access  Private (requires authentication)
 * @body    {
 *   callerId: number,
 *   receiverId: number,
 *   callType: 'voice' | 'video',
 *   platform: 'ANDROID' | 'IOS'
 * }
 */
router.post("/adtipcall", Auth.verifyToken, async (req, res) => {
  const { callerId, receiverId, callType, platform = 'ANDROID' } = req.body;

  // Validate request body
  if (!callerId || !receiverId || !callType) {
    return res.status(400).json({
      success: false,
      error: "Missing required fields: callerId, receiverId, callType"
    });
  }

  if (!['voice', 'video'].includes(callType)) {
    return res.status(400).json({
      success: false,
      error: "Invalid call type. Must be 'voice' or 'video'"
    });
  }

  if (callerId === receiverId) {
    return res.status(400).json({
      success: false,
      error: "Cannot call yourself"
    });
  }

  try {
    // Step 1: Validate users exist and get their details
    const usersQuery = `
      SELECT id, name, fcm_token, is_premium, premium_expires_at, dnd
      FROM users
      WHERE id IN (?, ?)
    `;
    const users = await queryRunner(usersQuery, [callerId, receiverId]);

    if (users.length !== 2) {
      return res.status(404).json({
        success: false,
        error: "One or both users not found or inactive"
      });
    }

    const caller = users.find(u => u.id === callerId);
    const receiver = users.find(u => u.id === receiverId);

    if (!caller || !receiver) {
      return res.status(404).json({
        success: false,
        error: "User details not found"
      });
    }

    // Step 2: Check if receiver has FCM token
    if (!receiver.fcm_token) {
      return res.status(400).json({
        success: false,
        error: "Receiver is not available for calls (no FCM token)"
      });
    }

    // Step 3: Check if users are blocked
    const blockedCheckQuery = `
      SELECT id FROM blocked_users
      WHERE (user_id = ? AND blocked_id = ? AND is_blocked = 1) OR (user_id = ? AND blocked_id = ? AND is_blocked = 1)
    `;
    const blockedUsers = await queryRunner(blockedCheckQuery, [callerId, receiverId, receiverId, callerId]);

    if (blockedUsers.length > 0) {
      console.log('[new_initiate_call] Call blocked - users have blocked each other:', { callerId, receiverId });
      return res.status(403).json({
        success: false,
        error: "Call not allowed - users are blocked"
      });
    }

    // Step 4: Check DND (Do Not Disturb) status
    if (receiver.dnd === 1) {
      return res.status(400).json({
        success: false,
        error: "Receiver has Do Not Disturb enabled"
      });
    }

    // Step 5: Check wallet balance for caller
    // NOTE: Wallet balance check is commented out as it's handled in the frontend
    // This prevents duplicate validation and database column name issues
    /*
    const walletQuery = `
      SELECT totalBalance FROM wallet
      WHERE createdby = ?
      ORDER BY id DESC
      LIMIT 1
    `;
    const walletResult = await queryRunner(walletQuery, [callerId]);

    if (walletResult.length === 0 || walletResult[0].totalBalance <= 0) {
      return res.status(400).json({
        success: false,
        error: "Insufficient wallet balance to make calls"
      });
    }
    */
    console.log('[new_initiate_call] Wallet balance check skipped - handled in frontend');

    // Step 6: Allow concurrent calls - Remove the restriction
    // Users can now receive calls even when already in an active call
    // The recipient can choose to accept (ending current call), decline, or ignore
    console.log('[new_initiate_call] Allowing concurrent calls - no active call restriction');

    // Step 7: Generate VideoSDK token
    let videoSDKToken;
    try {
      videoSDKToken = VideoSDKService.generateToken();
      console.log('[new_initiate_call] VideoSDK token generated successfully, length:', videoSDKToken.length);
    } catch (error) {
      console.error('VideoSDK token generation failed:', error);
      return res.status(500).json({
        success: false,
        error: "Failed to generate video call token"
      });
    }

    // Step 8: Create VideoSDK meeting
    let meetingId;
    try {
      console.log('[new_initiate_call] Creating VideoSDK meeting with region: us001');
      const meetingResponse = await VideoSDKService.createMeeting(videoSDKToken, 'us001');
      meetingId = meetingResponse.roomId;
      console.log('[new_initiate_call] VideoSDK meeting created successfully:', meetingId);

      // Validate meeting ID format
      if (!meetingId || typeof meetingId !== 'string') {
        throw new Error('Invalid meeting ID received from VideoSDK');
      }
    } catch (error) {
      console.error('VideoSDK meeting creation failed:', error);
      return res.status(500).json({
        success: false,
        error: "Failed to create video call room"
      });
    }

    // Step 7: Calculate call limits (simplified)
    const currentTime = new Date();
    let maxCallLimitMinutes = callType === 'video' ? 120 : 180; // 2 hours video, 3 hours voice
    let maxCallLimitSeconds = maxCallLimitMinutes * 60;

    const maxCallLimitDateTime = new Date(currentTime.getTime() + (maxCallLimitSeconds * 1000));

    // Step 8: Create call record in database
    const channelName = `call_${callerId}_${receiverId}_${Date.now()}`;
    const insertCallQuery = `
      INSERT INTO user_calls (
        caller_user_id, receiver_user_id, start_time, max_call_limit_time, 
        duration_seconds, created_at, call_type, channel_name, call_status,
        meeting_id, videosdk_token
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, ?)
    `;

    const callParams = [
      callerId, 
      receiverId, 
      currentTime, 
      maxCallLimitDateTime, 
      maxCallLimitSeconds, 
      currentTime, 
      `${callType}-call`, 
      channelName,
      meetingId,
      videoSDKToken
    ];

    const callResult = await queryRunner(insertCallQuery, callParams);
    const callId = callResult.insertId;

    // Step 9: Prepare FCM notification payload
    // Note: With concurrent calls enabled, the receiver may already be in a call
    // The FCM notification will still be sent, allowing the receiver to choose their response
    const sessionId = uuidv4();
    const fcmPayload = {
      data: {
        type: "CALL_INITIATED",
        callType: callType,
        callerName: caller.name || "Unknown Caller",
        callerId: callerId.toString(),
        receiverId: receiverId.toString(),
        callId: callId.toString(),
        sessionId: sessionId,
        meetingId: meetingId,
        token: videoSDKToken,
        channelName: channelName,
        maxDuration: maxCallLimitSeconds.toString(),
        platform: platform,
        uuid: uuidv4(),
        timestamp: Date.now().toString(),
        allowsConcurrentCalls: "true" // Flag to indicate concurrent calls are supported
      }
    };

    // Step 10: Send FCM notification to receiver
    console.log('FCM Token:', receiver.fcm_token);
    console.log('FCM Payload:', JSON.stringify(fcmPayload, null, 2));

    try {
      // Validate FCM token before sending
      if (!receiver.fcm_token || receiver.fcm_token.trim() === '') {
        console.warn('FCM Warning: Receiver has no FCM token, skipping notification');
      } else {
        const fcmResponse = await admin.messaging().sendToDevice(
          receiver.fcm_token,
          fcmPayload,
          {
            priority: "high",
            timeToLive: 30, // 30 seconds TTL for call notifications
            collapseKey: `call_${callId}`
          }
        );

        console.log('FCM notification sent:', {
          callId,
          success: fcmResponse.successCount,
          failure: fcmResponse.failureCount
        });

        // Log FCM delivery issues but don't fail the call
        if (fcmResponse.failureCount > 0) {
          console.warn('FCM delivery failed:', fcmResponse.results[0]?.error);
          console.warn('Call will proceed without FCM notification - receiver may need to manually check for calls');
        }
      }

    } catch (fcmError) {
      console.error('FCM Error Details:', {
        message: fcmError.message,
        code: fcmError.code,
        errorInfo: fcmError.errorInfo,
        stack: fcmError.stack
      });
      console.warn('Call will proceed without FCM notification - receiver may need to manually check for calls');
      // Don't fail the entire call request if FCM fails
    }

    // Step 11: Log call initiation for analytics - DISABLED due to missing call_logs table
    // const logQuery = `
    //   INSERT INTO call_logs (call_id, action, timestamp, details)
    //   VALUES (?, 'initiated', NOW(), ?)
    // `;
    // const logDetails = JSON.stringify({
    //   callType,
    //   platform,
    //   sessionId,
    //   fcmSuccess: true
    // });

    // try {
    //   await queryRunner(logQuery, [callId, logDetails]);
    // } catch (logError) {
    //   console.warn('Failed to log call initiation:', logError);
    //   // Don't fail the request for logging errors
    // }

    // Step 12: Return success response
    return res.status(200).json({
      success: true,
      message: "Call initiated successfully",
      data: {
        callId: callId,
        sessionId: sessionId,
        meetingId: meetingId,
        token: videoSDKToken,
        channelName: channelName,
        maxDuration: maxCallLimitSeconds,
        maxDurationMinutes: maxCallLimitMinutes,
        callerInfo: {
          id: caller.id,
          name: caller.name
        },
        receiverInfo: {
          id: receiver.id,
          name: receiver.name
        },
        callType: callType,
        platform: platform,
        startTime: currentTime.toISOString(),
        maxEndTime: maxCallLimitDateTime.toISOString()
      }
    });

  } catch (error) {
    console.error('Initiate call error:', error);
    
    return res.status(500).json({
      success: false,
      error: "Internal server error during call initiation",
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @route   POST /api/initiate-call/status
 * @desc    Update call status (accept, decline, end, missed)
 * @access  Private
 * @body    {
 *   callId: number,
 *   action: 'accept' | 'decline' | 'end' | 'missed',
 *   userId: number,
 *   duration?: number
 * }
 */
router.post("/initiate-call/status", Auth.verifyToken, async (req, res) => {
  const { callId, action, userId, duration } = req.body;

  if (!callId || !action || !userId) {
    return res.status(400).json({
      success: false,
      error: "Missing required fields: callId, action, userId"
    });
  }

  if (!['accept', 'decline', 'end', 'missed'].includes(action)) {
    return res.status(400).json({
      success: false,
      error: "Invalid action. Must be 'accept', 'decline', 'end', or 'missed'"
    });
  }

  try {
    // Get call details
    const callQuery = `
      SELECT c.*, caller.name as caller_name, caller.fcm_token as caller_fcm, 
             receiver.name as receiver_name, receiver.fcm_token as receiver_fcm
      FROM user_calls c
      JOIN users caller ON c.caller_user_id = caller.id
      JOIN users receiver ON c.receiver_user_id = receiver.id
      WHERE c.call_id = ?
    `;
    const callDetails = await queryRunner(callQuery, [callId]);

    if (!callDetails.length) {
      return res.status(404).json({
        success: false,
        error: "Call not found"
      });
    }

    const call = callDetails[0];

    // Verify user is part of this call
    if (call.caller_user_id !== userId && call.receiver_user_id !== userId) {
      return res.status(403).json({
        success: false,
        error: "Unauthorized to update this call"
      });
    }

    let updateQuery;
    let updateParams;
    let fcmType;
    let targetFcmToken;

    switch (action) {
      case 'accept':
        // When accepting a call, end any other active calls for this user
        const endOtherCallsQuery = `
          UPDATE user_calls
          SET call_status = 'ended_for_new_call', end_time = NOW()
          WHERE (caller_user_id = ? OR receiver_user_id = ?)
          AND call_status IN ('active', 'connected')
          AND call_id != ?
        `;
        await queryRunner(endOtherCallsQuery, [userId, userId, callId]);

        updateQuery = "UPDATE user_calls SET call_status = 'connected', connected_at = NOW() WHERE call_id = ?";
        updateParams = [callId];
        fcmType = "CALL_ACCEPTED";
        targetFcmToken = call.caller_fcm; // Notify caller
        break;

      case 'decline':
        updateQuery = "UPDATE user_calls SET call_status = 'declined', end_time = NOW() WHERE call_id = ?";
        updateParams = [callId];
        fcmType = "CALL_DECLINED";
        targetFcmToken = call.caller_fcm; // Notify caller
        break;

      case 'end':
        const actualDuration = duration || 0;
        updateQuery = `
          UPDATE user_calls 
          SET call_status = 'completed', end_time = NOW(), duration = ? 
          WHERE call_id = ?
        `;
        updateParams = [actualDuration, callId];
        fcmType = "CALL_ENDED";
        // Notify the other party
        targetFcmToken = call.caller_user_id === userId ? call.receiver_fcm : call.caller_fcm;
        break;

      case 'missed':
        updateQuery = "UPDATE user_calls SET call_status = 'missed', end_time = NOW() WHERE call_id = ?";
        updateParams = [callId];
        fcmType = "CALL_MISSED";
        targetFcmToken = call.caller_fcm; // Notify caller
        break;
    }

    // Update call status
    await queryRunner(updateQuery, updateParams);

    // Send FCM notification to the other party
    if (targetFcmToken && fcmType) {
      const notificationPayload = {
        data: {
          type: fcmType,
          callId: callId.toString(),
          callType: call.call_type,
          duration: duration ? duration.toString() : "0",
          timestamp: Date.now().toString()
        }
      };

      try {
        await admin.messaging().sendToDevice(targetFcmToken, notificationPayload, {
          priority: "high",
          timeToLive: 60
        });
      } catch (fcmError) {
        console.warn('Failed to send status update FCM:', fcmError);
        // Don't fail the request for FCM errors
      }
    }

    // Deactivate VideoSDK room if call is ending (billing optimization)
    if (['decline', 'end', 'missed'].includes(action) && call.meeting_id) {
      try {
        console.log(`[new_initiate_call] Deactivating VideoSDK room ${call.meeting_id} for billing optimization`);
        await VideoSDKService.deactivateRoom(call.meeting_id, call.videosdk_token);
        console.log(`[new_initiate_call] VideoSDK room ${call.meeting_id} deactivated successfully`);
      } catch (deactivateError) {
        console.warn('Failed to deactivate VideoSDK room:', deactivateError);
        // Don't fail the request for room deactivation errors
      }
    }

    return res.status(200).json({
      success: true,
      message: `Call ${action} updated successfully`,
      data: {
        callId: callId,
        action: action,
        status: action === 'accept' ? 'connected' : 
                action === 'decline' ? 'declined' :
                action === 'end' ? 'completed' : 'missed'
      }
    });

  } catch (error) {
    console.error('Update call status error:', error);
    return res.status(500).json({
      success: false,
      error: "Internal server error during status update"
    });
  }
});

module.exports = router;