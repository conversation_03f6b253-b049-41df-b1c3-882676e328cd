[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: armeabi-v7a", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vision-camera\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vision-camera\\android\\.cxx\\Debug\\4g346832\\armeabi-v7a\\android_gradle_build.json' was up-to-date", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vision-camera\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vision-camera\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]