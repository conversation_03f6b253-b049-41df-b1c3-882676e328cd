#!/usr/bin/env node

/**
 * Fix call_transactions foreign key constraint
 * 
 * This script fixes the foreign key constraint violation where call_transactions.call_id
 * references user_video_calls.call_id but VideoSDK calls are stored in user_calls table.
 * 
 * Usage: node scripts/fix_call_transactions_foreign_key.js
 */

const path = require('path');
const fs = require('fs');
const { queryRunner } = require('../dbConfig/queryRunner');

class CallTransactionsForeignKeyFixer {
  constructor() {
    this.scriptName = 'Fix Call Transactions Foreign Key';
  }

  async run() {
    console.log(`🚀 Starting ${this.scriptName}...`);
    console.log('⚠️  This will modify the call_transactions table foreign key constraint');
    
    try {
      await this.checkPrerequisites();
      await this.analyzeCurrentState();
      await this.fixForeignKeyConstraint();
      await this.verifyFix();
      
      console.log('✅ Foreign key constraint fix completed successfully!');
      console.log('📝 VideoSDK calls can now create transactions without foreign key violations.');
      
    } catch (error) {
      console.error('❌ Error during foreign key fix:', error.message);
      console.error('Stack trace:', error.stack);
      process.exit(1);
    }
  }

  async checkPrerequisites() {
    console.log('🔍 Checking prerequisites...');
    
    // Check if call_transactions table exists
    const [callTransactionsTable] = await queryRunner(
      "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'call_transactions'"
    );
    
    if (callTransactionsTable.count === 0) {
      throw new Error('call_transactions table not found');
    }
    
    // Check if user_calls table exists
    const [userCallsTable] = await queryRunner(
      "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'user_calls'"
    );
    
    if (userCallsTable.count === 0) {
      throw new Error('user_calls table not found');
    }
    
    console.log('✅ Prerequisites check passed');
  }

  async analyzeCurrentState() {
    console.log('📊 Analyzing current state...');
    
    // Check current foreign key constraints
    const constraints = await queryRunner(`
      SELECT 
        CONSTRAINT_NAME,
        TABLE_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'call_transactions' 
        AND CONSTRAINT_NAME LIKE '%ibfk%'
    `);
    
    console.log('📋 Current foreign key constraints:');
    constraints.forEach(constraint => {
      console.log(`   ${constraint.CONSTRAINT_NAME}: ${constraint.COLUMN_NAME} -> ${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME}`);
    });
    
    // Check existing transactions count
    const [transactionCount] = await queryRunner('SELECT COUNT(*) as count FROM call_transactions');
    console.log(`📊 Existing call_transactions records: ${transactionCount.count}`);
    
    // Check for orphaned transactions
    const orphanedTransactions = await queryRunner(`
      SELECT COUNT(*) as count
      FROM call_transactions ct
      LEFT JOIN user_calls uc ON ct.call_id = uc.call_id
      WHERE uc.call_id IS NULL
    `);
    
    console.log(`🔍 Orphaned transactions (referencing non-existent user_calls): ${orphanedTransactions[0].count}`);
    
    if (orphanedTransactions[0].count > 0) {
      console.log('⚠️  Warning: Some transactions reference call_ids that don\'t exist in user_calls');
      console.log('   These transactions may need to be cleaned up or migrated');
      
      // Show sample orphaned transactions
      const sampleOrphaned = await queryRunner(`
        SELECT ct.id, ct.call_id, ct.call_type, ct.created_at
        FROM call_transactions ct
        LEFT JOIN user_calls uc ON ct.call_id = uc.call_id
        WHERE uc.call_id IS NULL
        LIMIT 5
      `);
      
      console.log('   Sample orphaned transactions:');
      sampleOrphaned.forEach(tx => {
        console.log(`     ID: ${tx.id}, call_id: ${tx.call_id}, type: ${tx.call_type}, created: ${tx.created_at}`);
      });
    }
  }

  async fixForeignKeyConstraint() {
    console.log('🔧 Fixing foreign key constraint...');
    
    try {
      // Drop the existing foreign key constraint
      console.log('   Dropping existing constraint call_transactions_ibfk_3...');
      await queryRunner('ALTER TABLE call_transactions DROP FOREIGN KEY call_transactions_ibfk_3');
      
      // Add new foreign key constraint
      console.log('   Adding new constraint referencing user_calls...');
      await queryRunner(`
        ALTER TABLE call_transactions 
        ADD CONSTRAINT call_transactions_ibfk_3 
        FOREIGN KEY (call_id) REFERENCES user_calls(call_id) 
        ON DELETE CASCADE
      `);
      
      console.log('✅ Foreign key constraint updated successfully');
      
    } catch (error) {
      if (error.message.includes('foreign key constraint does not exist')) {
        console.log('⚠️  Constraint call_transactions_ibfk_3 does not exist, creating new one...');
        await queryRunner(`
          ALTER TABLE call_transactions 
          ADD CONSTRAINT call_transactions_ibfk_3 
          FOREIGN KEY (call_id) REFERENCES user_calls(call_id) 
          ON DELETE CASCADE
        `);
        console.log('✅ New foreign key constraint created successfully');
      } else {
        throw error;
      }
    }
  }

  async verifyFix() {
    console.log('🔍 Verifying fix...');
    
    // Check the new constraint
    const newConstraints = await queryRunner(`
      SELECT 
        CONSTRAINT_NAME,
        TABLE_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'call_transactions' 
        AND CONSTRAINT_NAME = 'call_transactions_ibfk_3'
    `);
    
    if (newConstraints.length === 0) {
      throw new Error('New foreign key constraint was not created');
    }
    
    const constraint = newConstraints[0];
    if (constraint.REFERENCED_TABLE_NAME !== 'user_calls') {
      throw new Error(`Foreign key constraint references wrong table: ${constraint.REFERENCED_TABLE_NAME}`);
    }
    
    console.log('✅ New foreign key constraint verified:');
    console.log(`   ${constraint.CONSTRAINT_NAME}: ${constraint.COLUMN_NAME} -> ${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME}`);
  }
}

// Run the script if called directly
if (require.main === module) {
  const fixer = new CallTransactionsForeignKeyFixer();
  fixer.run().catch(error => {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  });
}

module.exports = CallTransactionsForeignKeyFixer;
