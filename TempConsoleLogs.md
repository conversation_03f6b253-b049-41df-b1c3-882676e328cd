TipCallScreenSimple.tsx:868 Warning: useInsertionEffect must not schedule updates.
    at Animated(View) (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:111440:47)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102432:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24391:79)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102432:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24391:79)
    at AppContainer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102323:25)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102432:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24391:79)
    at VirtualizedListContextResetter (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:115934:24)
    at RCTModalHostView (<anonymous>)
    at Modal (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:129256:36)
    at CallConfirmationAlert (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:416106:23)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102432:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24391:79)
    at TipCallScreenSimple (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:412895:97)
    at StaticContainer (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163294:17)
    at EnsureSingleNavigator (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:158911:24)
    at SceneView (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:163134:22)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102432:43)
    at RCTView (<anonymous>)
    at View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:102432:43)
    at CssInterop.View (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:24391:79)
    at Animated(View) (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:111440:47)
    at Background (http://localhost:8081/index.bundle//&platform=android&dev=true&lazy=tru
anonymous @ console.js:654
overrideMethod @ backend.js:17042
reactConsoleErrorHandler @ ExceptionsManager.js:182
anonymous @ setUpDeveloperTools.js:40
registerError @ LogBox.js:260
anonymous @ LogBox.js:80
printWarning @ ReactFabric-dev.js:160
error$jscomp$0 @ ReactFabric-dev.js:151
scheduleUpdateOnFiber @ ReactFabric-dev.js:11403
dispatchSetStateInternal @ ReactFabric-dev.js:5873
dispatchSetState @ ReactFabric-dev.js:5826
anonymous @ TipCallScreenSimple.tsx:868
anonymous @ CallConfirmationAlert.tsx:129
cb @ AnimatedImplementation.js:388
anonymous @ AnimatedValue.js:325
__notifyAnimationEnd @ Animation.js:179
stop @ TimingAnimation.js:172
stopAnimation @ AnimatedValue.js:258
stop @ AnimatedImplementation.js:242
anonymous @ AnimatedImplementation.js:407
stop @ AnimatedImplementation.js:406
cb @ AnimatedImplementation.js:393
anonymous @ AnimatedValue.js:325
__notifyAnimationEnd @ Animation.js:179
stop @ TimingAnimation.js:172
stopAnimation @ AnimatedValue.js:258
__detach @ AnimatedValue.js:116
__removeChild @ AnimatedWithChildren.js:64
__detach @ AnimatedStyle.js:211
__removeChild @ AnimatedWithChildren.js:64
__detach @ AnimatedProps.js:175
anonymous @ createAnimatedPropsHook.js:290
reactStackBottomFrame @ ReactFabric-dev.js:14861
runWithFiberInDEV @ ReactFabric-dev.js:571
commitHookEffectListUnmount @ ReactFabric-dev.js:9665
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10307
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10282
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10282
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10368
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10282
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10368
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10368
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10318
recursivelyTraverseDeletionEffects @ ReactFabric-dev.js:10249
commitDeletionEffectsOnFiber @ ReactFabric-dev.js:10282
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10427
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10461
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10559
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10533
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10559
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10533
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10559
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10533
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10461
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10461
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10480
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10599
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10445
recursivelyTraverseMutationEffects @ ReactFabric-dev.js:10434
commitMutationEffectsOnFiber @ ReactFabric-dev.js:10493
commitMutationEffects @ ReactFabric-dev.js:10419
commitRootImpl @ ReactFabric-dev.js:12443
commitRootWhenReady @ ReactFabric-dev.js:11705
performWorkOnRoot @ ReactFabric-dev.js:11652
performWorkOnRootViaSchedulerTask @ ReactFabric-dev.js:2807
Show 507 more frames
Show less
useQueries.ts:973 [useUsers] getNextPageParam: {totalRecords: 59555, currentPage: 1, limit: 20, currentPageRecords: 20, hasMore: true, nextPage: 2}
PermissionManagerService.ts:268 [PermissionManager] Call permissions result: {microphone: true, camera: false}
ProductionLogger.ts:117 [CALL:CallController] Starting comprehensive cleanup undefined
callStateCleanup.ts:76 [CallStateCleanup] Starting comprehensive cleanup
callStateCleanup.ts:112 [CallStateCleanup] Cleaning up media service
MediaService.ts:119 [MediaService] Starting comprehensive meeting cleanup
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Starting comprehensive service reset undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Clearing component instance tracking undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Service reset complete undefined
MediaService.ts:167 [MediaService] Comprehensive meeting cleanup completed
callStateCleanup.ts:136 [CallStateCleanup] Resetting VideoSDK service
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Starting comprehensive service reset undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Clearing component instance tracking undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Service reset complete undefined
callStateCleanup.ts:150 [CallStateCleanup] Cleaning up call store
callStoreSimplified.ts:58 [CallStore] Performing comprehensive reset
callStoreSimplified.ts:64 [CallStore] Reset complete
callStateCleanup.ts:162 [CallStateCleanup] Cleaning up WebRTC connections
ProductionLogger.ts:51 [DEBUG:UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
callStateCleanup.ts:193 [CallStateCleanup] Clearing participant cache
callStateCleanup.ts:235 [CallStateCleanup] Forcing garbage collection
callStateCleanup.ts:97 [CallStateCleanup] Comprehensive cleanup completed successfully
ProductionLogger.ts:117 [CALL:CallController] Comprehensive cleanup complete undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Starting VideoSDK initialization with config: {region: 'us001', websocketConfig: {…}}
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Registering VideoSDK... undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] WebSocket connection attempt 1/3 undefined
callStateCleanup.ts:225 [CallStateCleanup] Participant cache clearing completed
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Validating VideoSDK connection... undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] WebSocket connection established on attempt 1 undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] VideoSDK initialization complete with WebSocket ready undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Clearing existing meeting state to prevent conflicts undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Meeting state cleared successfully undefined
ProductionLogger.ts:117 [CALL:CallController] Generated session ID {sessionId: 'bcfe8a6b-25dd-46da-8e9a-2049d6383727'}
ProductionLogger.ts:117 [CALL:CallController] Status changed: idle -> outgoing undefined
ProductionLogger.ts:117 [CALL:CallController] Status changed: outgoing -> connecting undefined
ProductionLogger.ts:126 [VideoSDK:VideoSDKService] Already initialized and WebSocket ready undefined
ProductionLogger.ts:51 [DEBUG:UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
ProductionLogger.ts:117 [CALL:[PersistentMeetingManager] Starting new call:] bcfe8a6b-25dd-46da-8e9a-2049d6383727 undefined
ProductionLogger.ts:117 [CALL:[PersistentMeetingManager] Persistent call started successfully] bcfe8a6b-25dd-46da-8e9a-2049d6383727 undefined
ProductionLogger.ts:117 [CALL:CallController] Making async consolidated call API request {callerId: 58422, receiverId: 58463, callType: 'voice', platform: 'ANDROID'}
TipCallScreenSimple.tsx:841 📞 [TipCallScreenSimple] Call initiation result: {success: true, recipientId: '58463', callType: 'voice', timestamp: '2025-07-29T11:49:45.327Z'}
TipCallScreenSimple.tsx:849 ✅ [TipCallScreenSimple] Call started successfully - API calls should be logged by CallController
console.js:654 Bluetooth Connect Permission Granted
ApiService.ts:246 Request to protected endpoint: /api/adtipcall. Attempting to add Authorization header.
ApiService.ts:253 Authorization header added to request for: /api/adtipcall
ApiService.ts:260 🚀 API REQUEST: {method: 'POST', url: '/api/adtipcall', baseURL: 'https://api.adtip.in', fullURL: 'https://api.adtip.in/api/adtipcall', headers: {…}, params: undefined, data: {…}, timeout: 60000, timestamp: '2025-07-29T11:49:45.380Z'}
ProductionLogger.ts:117 [CALL:[PersistentMeetingContent] Setting meeting reference for session:] bcfe8a6b-25dd-46da-8e9a-2049d6383727 undefined
MediaService.ts:232 [MediaService] Meeting reference set: true (meetingId: undefined)
ProductionLogger.ts:117 [CALL:[PersistentMeetingContent] Join attempt 1 for session:] bcfe8a6b-25dd-46da-8e9a-2049d6383727 undefined
ProductionLogger.ts:117 [CALL:[PersistentMeetingContent] Joining meeting with ID:] temp-bcfe8a6b-25dd-46da-8e9a-2049d6383727 undefined
ProductionLogger.ts:117 [CALL:[PersistentMeetingContent] Successfully joined meeting] temp-bcfe8a6b-25dd-46da-8e9a-2049d6383727 undefined
ProductionLogger.ts:117 [CALL:CallController] Status changed: connecting -> in_call undefined
ProductionLogger.ts:51 [DEBUG:UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
ProductionLogger.ts:96 [NET:ApiService] POST https://api.adtip.in/api/adtipcall (200) {statusText: undefined, headers: {…}, data: {…}, timestamp: '2025-07-29T11:49:45.846Z'}
ProductionLogger.ts:117 [CALL:CallController] Async consolidated call API response {success: true, sessionId: '836e4f18-0199-470f-9e6e-39ac1fbe1a4d', meetingId: 'gmlo-cqhj-5m45'}
ProductionLogger.ts:117 [CALL:CallController] Updating session with real API data {apiSessionId: '836e4f18-0199-470f-9e6e-39ac1fbe1a4d', meetingId: 'gmlo-cqhj-5m45', backendCallId: 69}
ProductionLogger.ts:117 [CALL:CallController] Session updated successfully with real API data undefined
ProductionLogger.ts:51 [DEBUG:UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:427499 Error while trying to reconnect websocket error
