/**
 * Test script to verify the backend call record management fix
 * 
 * This script tests:
 * 1. Call record creation in user_calls table
 * 2. Call record lookup during end call
 * 3. Proper callId synchronization
 * 
 * Run with: node test_call_record_fix.js
 */

const { queryRunner } = require('./dbConfig/queryRunner');
const VideoSDKCallService = require('./services/VideoSDKCallService');

async function testCallRecordFix() {
  console.log('🧪 Testing Backend Call Record Management Fix...\n');

  try {
    // Test data
    const testCallerId = 1; // Assuming user ID 1 exists
    const testReceiverId = 2; // Assuming user ID 2 exists

    console.log('📞 Step 1: Testing call start (creates record in user_calls)');
    
    // Test starting a call using VideoSDK service
    const startResult = await VideoSDKCallService.startVideoSDKCall(testCallerId, testReceiverId);
    
    if (!startResult.status) {
      console.error('❌ Call start failed:', startResult.message);
      return;
    }
    
    console.log('✅ Call started successfully');
    console.log('📋 Call ID:', startResult.callId);
    console.log('⏰ Start Time:', startResult.startTime);
    
    const callId = startResult.callId;

    // Verify record exists in user_calls table
    console.log('\n🔍 Step 2: Verifying record exists in user_calls table');
    const verifyQuery = `SELECT * FROM user_calls WHERE call_id = ?`;
    const [callRecord] = await queryRunner(verifyQuery, [callId]);
    
    if (!callRecord) {
      console.error('❌ Call record not found in user_calls table');
      return;
    }
    
    console.log('✅ Call record found in user_calls table');
    console.log('📋 Record details:', {
      call_id: callRecord.call_id,
      caller_user_id: callRecord.caller_user_id,
      receiver_user_id: callRecord.receiver_user_id,
      call_type: callRecord.call_type,
      call_status: callRecord.call_status
    });

    // Wait a moment to simulate call duration
    console.log('\n⏳ Step 3: Simulating call duration (2 seconds)...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test ending the call
    console.log('\n🔚 Step 4: Testing call end (should find record in user_calls)');
    const endResult = await VideoSDKCallService.endVideoSDKCall(testCallerId, testReceiverId, callId);
    
    if (!endResult.status) {
      console.error('❌ Call end failed:', endResult.message);
      return;
    }
    
    console.log('✅ Call ended successfully');
    console.log('💰 Caller debited:', endResult.caller_debited_charge);
    console.log('💰 Receiver credited:', endResult.receiver_credited_charge);

    // Verify record was updated
    console.log('\n🔍 Step 5: Verifying record was updated with end_time');
    const [updatedRecord] = await queryRunner(verifyQuery, [callId]);
    
    if (!updatedRecord.end_time) {
      console.error('❌ Call record was not updated with end_time');
      return;
    }
    
    console.log('✅ Call record updated successfully');
    console.log('📋 Updated record:', {
      call_id: updatedRecord.call_id,
      start_time: updatedRecord.start_time,
      end_time: updatedRecord.end_time,
      duration: updatedRecord.duration
    });

    console.log('\n🎉 All tests passed! Backend call record management fix is working correctly.');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error('📋 Error details:', error);
  }
}

// Run the test
if (require.main === module) {
  testCallRecordFix()
    .then(() => {
      console.log('\n✅ Test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testCallRecordFix };
