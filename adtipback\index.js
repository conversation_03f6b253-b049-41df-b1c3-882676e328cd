require("dotenv").config();
const express = require("express");
const app = require("./config/app.js");
const cors = require("cors");
const http = require("http");
const https = require("https");
const fs = require("fs");
const moment = require("moment");
const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

// Import swagger-ui-express and the generated Swagger JSON
const swaggerUi = require("swagger-ui-express");
const swaggerDocument = require("./swagger-output.json");
const basicAuth = require("basic-auth");

// Import database health service
const databaseHealthService = require("./services/DatabaseHealthService");

// Middleware for basic authentication
const swaggerAuthMiddleware = (req, res, next) => {
  const user = basicAuth(req);
  const username = process.env.SWAGGER_USERNAME || "admin";
  const password = process.env.SWAGGER_PASSWORD || "admin";

  if (!user || user.name !== username || user.pass !== password) {
    res.set("WWW-Authenticate", 'Basic realm="Swagger UI"');
    return res.status(401).send("Unauthorized");
  }
  next();
};

// Serve Swagger UI at /api-docs with authentication
app.use("/api-docs", swaggerAuthMiddleware, swaggerUi.serve, swaggerUi.setup(swaggerDocument));

// Configure CORS
// Environment-based CORS configuration
const isDevelopment = process.env.NODE_ENV !== 'production';
const allowedOrigins = isDevelopment 
  ? [
      'https://adtip.in', 
      'https://www.adtip.in',
      'http://localhost:3000',
      'http://localhost:8081',
      'http://*************:3000',
      'http://*************:8081',
      'http://*************:7082',
      'http://********:7082', // Android emulator
      'http://********:7082', // Genymotion emulator
      'http://127.0.0.1:3000',
      'http://127.0.0.1:8081',
      'http://127.0.0.1:7082',
    ]
  : [
      'https://adtip.in', 
      'https://www.adtip.in'
    ];

app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or Postman)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  optionsSuccessStatus: 200 // Some legacy browsers (IE11, various SmartTVs) choke on 204
}));
app.options("*", cors());

// Create HTTP server (for local development)
const httpServer = http.createServer(app);

// DISABLED: Socket.IO Chat Service (replaced with FCM high priority notifications)
// Use FCM chat API endpoints for reliable messaging instead
/*
const ChatSocketService = require('./services/ChatSocketService');
ChatSocketService.initialize(httpServer);
console.log('✅ Socket.IO Chat Service initialized');
*/
console.log('ℹ️  Socket.IO Chat Service disabled - using FCM notifications for messaging');

// Create HTTPS server (for production)
let httpsServer;
try {
  httpsServer = https.createServer({
    cert: fs.readFileSync("/etc/letsencrypt/live/api.adtip.in/fullchain.pem"),
    key: fs.readFileSync("/etc/letsencrypt/live/api.adtip.in/privkey.pem"),
  }, app);
} catch (err) {
  console.error("Failed to load SSL certificates for HTTPS server:", err.message);
  console.log("Falling back to HTTP server only...");
}

// Set up WebSocket servers
const WebSocket = require("ws");
const url = require("url");

// Create a single WebSocket server with proper control frame handling
const wss = new WebSocket.Server({
  server: httpsServer || httpServer,
  perMessageDeflate: false, // Disable compression to avoid control frame issues
  clientTracking: true,
  verifyClient: (info) => {
    // Add custom verification if needed
    console.log('WebSocket connection attempt from:', info.origin);
    return true;
  }
});

const setupLudoWebSocket = require("./websocket/ludoWebSocket");
const setupChatWebSocket = require("./websocket/chatWebSocket");

// Handle WebSocket connections with path-based routing
wss.on('connection', (ws, req) => {
  const pathname = url.parse(req.url).pathname;
  console.log('WebSocket connection established for path:', pathname);
  
  if (pathname === '/ludo') {
    // Set up Ludo WebSocket handling
    setupLudoWebSocket(ws, req);
  } else if (pathname === '/chat') {
    // Set up Chat WebSocket handling
    setupChatWebSocket(ws, req, wss);
  } else {
    console.log('Unknown WebSocket path:', pathname);
    ws.close(1000, 'Unknown path');
  }
});

// Add server-level error handling
wss.on('error', (error) => {
  console.error('WebSocket Server Error:', error);
});

// Periodic cleanup of dead connections
setInterval(() => {
  wss.clients.forEach((ws) => {
    if (ws.isAlive === false) {
      console.log('Terminating dead connection');
      return ws.terminate();
    }
    ws.isAlive = false;
    ws.ping();
  });
}, 30000);

// Route setup
app.use("/api", require("./routes/api-routes").router);
app.use("/", require("./routes/route.js").router);
app.use("/", require("./routes/render.js"));
app.use("/api/call", require("./routes/firebaseCallRoutes"));
app.use("/api", require("./services/new_initiate_call"));

// Add new chat routes
app.use("/api/chat", require("./routes/chatRoutes"));

// Add health monitoring routes
app.use("/api/health", require("./routes/healthRoutes"));

// Add deep link routes
app.use("/api/deeplink", require("./routes/deepLinkRoutes"));
app.use("/", require("./routes/deepLinkRoutes")); // For universal links

// Error handler
const {
  apiErrorHandler,
  unexpectedErrorHandler,
} = require("./config/error.js");
app.use(apiErrorHandler);
unexpectedErrorHandler();

// Start database health monitoring
console.log("Starting database health monitoring...");
databaseHealthService.startMonitoring();

// Start VideoSDK session cleanup service - DISABLED due to missing database columns
// const videoSDKSessionCleanup = require("./services/VideoSDKSessionCleanup");
// console.log("Starting VideoSDK session cleanup service...");
// videoSDKSessionCleanup.start();

// Cron jobs
const cron = require("node-cron");
const { checkPlanExpiry } = require("./scripts/checkPlanExpiry");
const { checkPendingTransactions } = require("./scripts/checkPendingTransactions");
const { checkContentCreatorPlanExpiry } = require("./scripts/checkContentCreatorPlanExpiry");
const { exec } = require('child_process');

cron.schedule("0 0 * * *", async () => {
  console.log("Running plan expiry check...Corn Job1");
  await checkPlanExpiry();
});

// Schedule daily cleanup of old messages
cron.schedule('0 0 * * *', () => {
  console.log('Running daily cleanup of old chat messages...');
  exec('node scripts/cleanupOldMessages.js', (err, stdout, stderr) => {
    if (err) {
      console.error(`Error during scheduled cleanup: ${stderr}`);
      return;
    }
    console.log(`Scheduled cleanup output: ${stdout}`);
  });
}, {
  scheduled: true,
  timezone: "Asia/Kolkata" // Example: Indian Standard Time
});

// Start the HTTP server (for local development)
const HTTP_PORT = process.env.HTTP_PORT || 7082;
httpServer.listen(HTTP_PORT, () => {
  console.log("MOMENT UPDATED TIME.....", updatedTime);
  console.log(`HTTP Server started and running on port: ${HTTP_PORT}`);
  console.log("Database health monitoring is active");
});

// Start the HTTPS server (for production) on port 443
if (httpsServer) {
  const HTTPS_PORT = process.env.HTTPS_PORT || 443;
  httpsServer.listen(HTTPS_PORT, () => {
    console.log("MOMENT UPDATED TIME.....", updatedTime);
    console.log(`HTTPS Server started and running on port: ${HTTPS_PORT}`);
    console.log("Database health monitoring is active");
  });
}

// Graceful shutdown handling
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully...');

  // Stop database health monitoring
  databaseHealthService.stopMonitoring();

  // Stop VideoSDK session cleanup service
  videoSDKSessionCleanup.stop();
  
  // Close database connections
  const mysqlConnection = require('./dbConfig/dbconnection');
  await mysqlConnection.closePool();
  
  // Close servers
  if (httpsServer) httpsServer.close();
  httpServer.close();
  
  console.log('Graceful shutdown completed');
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully...');

  // Stop database health monitoring
  databaseHealthService.stopMonitoring();

  // Stop VideoSDK session cleanup service - DISABLED
  // videoSDKSessionCleanup.stop();
  
  // Close database connections
  const mysqlConnection = require('./dbConfig/dbconnection');
  await mysqlConnection.closePool();
  
  // Close servers
  if (httpsServer) httpsServer.close();
  httpServer.close();
  
  console.log('Graceful shutdown completed');
  process.exit(0);
});

// Check content creator plan expiry daily at midnight (uncomment if needed)
/*
cron.schedule("0 0 * * *", async () => {
  console.log("Running content creator plan expiry check...Corn Job3");
  await checkContentCreatorPlanExpiry();
});
*/

// Check pending transactions every minute (uncomment if needed)
/*
cron.schedule("* * * * *", async () => {
  console.log("Running pending transactions checking every minute...Corn Job2");
  await checkPendingTransactions();
});
*/

