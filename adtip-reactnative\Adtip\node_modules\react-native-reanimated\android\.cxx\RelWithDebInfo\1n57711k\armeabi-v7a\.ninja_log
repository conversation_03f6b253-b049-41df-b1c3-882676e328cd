# ninja log v5
9	47	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/1n57711k/armeabi-v7a/CMakeFiles/cmake.verify_globs	fe24790902deadfe
32	5124	7754078743971487	src/main/cpp/worklets/CMakeFiles/worklets.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o	5a1392bc017c3512
107	5266	7754078745321501	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o	83e5d0d89bcc1e0a
95	6542	7754078756261532	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/ReanimatedVersion.cpp.o	abcbc91dddcebd26
83	6813	7754078758611492	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/JSLogger.cpp.o	ef3943a6d2d7d4fb
21	8052	7754078773081493	src/main/cpp/worklets/CMakeFiles/worklets.dir/b3753698fca1a6c32856249ade94ea38/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o	4267ca17c40c80db
46	8664	7754078778681525	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/AsyncQueue.cpp.o	6854171a97fb12ec
3	9246	7754078784901485	src/main/cpp/worklets/CMakeFiles/worklets.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/worklets/Registries/EventHandlerRegistry.cpp.o	42abad8fcaa556ce
71	9319	7754078785671505	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/JSISerializer.cpp.o	9507178f035e5748
59	10080	7754078792371497	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/SharedItems/Shareables.cpp.o	c47e71a65769220
5153	10119	7754078793371538	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/JSScheduler.cpp.o	a8d6ac86930a42c4
6593	11608	7754078806031508	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/UIScheduler.cpp.o	952f2358a0a065a2
5286	12124	7754078812991505	src/main/cpp/worklets/CMakeFiles/worklets.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o	8f682ebbb6be5451
6854	12280	7754078814931498	src/main/cpp/worklets/CMakeFiles/worklets.dir/a5f6d423558b9c215d9145ca2fb8ac71/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o	1a0021c09942f94b
10119	12383	7754078817181508	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o	fb033cd5da0b031
8061	12728	7754078819581507	src/main/cpp/worklets/CMakeFiles/worklets.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp.o	b6eead7593a823a6
8679	14536	7754078838091478	src/main/cpp/worklets/CMakeFiles/worklets.dir/b3753698fca1a6c32856249ade94ea38/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o	3468764ab52fa529
12309	17921	7754078870701514	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/ff5daf37e8cf25dbc59d661de1d5a693/AnimatedSensor/AnimatedSensorModule.cpp.o	95a597754a3b4870
9337	18837	7754078880371524	src/main/cpp/worklets/CMakeFiles/worklets.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o	3380231eefee0c5
12	19102	7754078882621516	src/main/cpp/worklets/CMakeFiles/worklets.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o	e9f5b0ce4384124a
10081	20240	7754078893331506	src/main/cpp/worklets/CMakeFiles/worklets.dir/b3753698fca1a6c32856249ade94ea38/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o	90757b42a8ded822
9272	21310	7754078905311494	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o	ba1a14297e622dbf
12384	24006	7754078932491498	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/reanimated/Fabric/PropsRegistry.cpp.o	8b3f32f0ca107d88
12154	25963	7754078951401526	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o	af1271c72f79fd18
18865	26508	7754078957101500	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/ff5daf37e8cf25dbc59d661de1d5a693/LayoutAnimations/LayoutAnimationsManager.cpp.o	a6ff8e340d88be6e
25979	28016	7754078973441500	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/610c13b34e6eff63bf2762b3a0322c47/Common/cpp/reanimated/Tools/FeaturesConfig.cpp.o	f73098e787abb9b5
11614	29727	7754078989771506	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o	8046e4f9fddac4ad
21318	29919	7754078991301520	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/ff5daf37e8cf25dbc59d661de1d5a693/RuntimeDecorators/UIRuntimeDecorator.cpp.o	da559e136da36e82
29729	30333	7754078995291521	../../../../build/intermediates/cxx/RelWithDebInfo/1n57711k/obj/armeabi-v7a/libworklets.so	b81245afbebaacd5
12729	30725	7754078998431501	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o	e49b60092b543811
26534	32875	7754079020761511	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/ff5daf37e8cf25dbc59d661de1d5a693/NativeModules/ReanimatedModuleProxySpec.cpp.o	97a4d2e5847953fd
17932	33237	7754079022741516	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o	ed050ebef08b2327
19125	33800	7754079030121517	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/6ee4d8ed6afcabfa3541557a7218e0cc/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o	a8ab29799ed7fa7d
14537	34882	7754079041101507	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/ff5daf37e8cf25dbc59d661de1d5a693/LayoutAnimations/LayoutAnimationsProxy.cpp.o	e99966db79296138
20255	37297	7754079065991498	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/ff5daf37e8cf25dbc59d661de1d5a693/LayoutAnimations/LayoutAnimationsUtils.cpp.o	94d3b1fe2ac7f40f
24019	39706	7754079089911505	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/ff5daf37e8cf25dbc59d661de1d5a693/RuntimeDecorators/RNRuntimeDecorator.cpp.o	2ec412147491e6a3
29925	39718	7754079090021499	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o	d4e4f054c0acacbb
32910	40299	7754079096061478	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o	e736d839e3715170
30333	43746	7754079130661497	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o	5eadd64af56acbba
30766	47453	7754079167621451	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o	d6fcd69e4aa0b9be
28017	48354	7754079176681513	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/ff5daf37e8cf25dbc59d661de1d5a693/NativeModules/ReanimatedModuleProxy.cpp.o	4104072f5c83af8c
48355	48550	7754079178751482	../../../../build/intermediates/cxx/RelWithDebInfo/1n57711k/obj/armeabi-v7a/libreanimated.so	6991674b2b32d4c5
2	51	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/1n57711k/armeabi-v7a/CMakeFiles/cmake.verify_globs	fe24790902deadfe
1	66	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/1n57711k/armeabi-v7a/CMakeFiles/cmake.verify_globs	fe24790902deadfe
2	45	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/1n57711k/armeabi-v7a/CMakeFiles/cmake.verify_globs	fe24790902deadfe
5	52	0	F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/1n57711k/armeabi-v7a/CMakeFiles/cmake.verify_globs	fe24790902deadfe
