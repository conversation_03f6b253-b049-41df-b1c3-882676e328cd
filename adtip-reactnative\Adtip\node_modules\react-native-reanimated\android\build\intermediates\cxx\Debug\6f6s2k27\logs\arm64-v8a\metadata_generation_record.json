[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6f6s2k27\\arm64-v8a\\android_gradle_build.json' was up-to-date", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]