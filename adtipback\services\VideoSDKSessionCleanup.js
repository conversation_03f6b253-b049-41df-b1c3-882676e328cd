const VideoSDKService = require('./videosdk_service');
const { queryRunner } = require('../dbConfig/queryRunner');

/**
 * VideoSDK Session Cleanup Service
 * Automatically cleans up empty sessions to prevent unnecessary billing
 */
class VideoSDKSessionCleanup {
  constructor() {
    this.cleanupInterval = null;
    this.isRunning = false;
    this.cleanupIntervalMs = 60000; // Check every 1 minute
    this.emptyRoomThresholdMs = 60000; // Cleanup rooms empty for 1+ minutes
  }

  /**
   * Start the cleanup service
   */
  start() {
    if (this.isRunning) {
      console.log('[VideoSDKSessionCleanup] Service already running');
      return;
    }

    console.log('[VideoSDKSessionCleanup] Starting session cleanup service');
    this.isRunning = true;
    
    // Run initial cleanup
    this.performCleanup();
    
    // Set up periodic cleanup
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, this.cleanupIntervalMs);
  }

  /**
   * Stop the cleanup service
   */
  stop() {
    if (!this.isRunning) {
      console.log('[VideoSDKSessionCleanup] Service not running');
      return;
    }

    console.log('[VideoSDKSessionCleanup] Stopping session cleanup service');
    this.isRunning = false;
    
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * Perform cleanup of empty sessions
   */
  async performCleanup() {
    try {
      console.log('[VideoSDKSessionCleanup] Starting cleanup cycle');
      
      // Get active calls with VideoSDK meetings
      const activeCallsQuery = `
        SELECT call_id, meeting_id, videosdk_token, start_time, call_status,
               caller_user_id, receiver_user_id
        FROM user_calls 
        WHERE call_status IN ('active', 'connected') 
        AND meeting_id IS NOT NULL 
        AND meeting_id != ''
        ORDER BY start_time ASC
      `;
      
      const activeCalls = await queryRunner(activeCallsQuery);
      console.log(`[VideoSDKSessionCleanup] Found ${activeCalls.length} active calls with VideoSDK meetings`);
      
      let cleanedCount = 0;
      let errorCount = 0;
      
      for (const call of activeCalls) {
        try {
          // Check if room is empty and cleanup if needed
          const result = await VideoSDKService.checkAndCleanupEmptyRoom(
            call.meeting_id, 
            call.videosdk_token
          );
          
          if (result.deactivated) {
            // Update call status in database
            await this.updateCallStatusAfterCleanup(call.call_id, 'ended_empty_room');
            cleanedCount++;
            
            console.log(`[VideoSDKSessionCleanup] Cleaned up empty room: ${call.meeting_id} for call ${call.call_id}`);
          }
          
        } catch (error) {
          console.error(`[VideoSDKSessionCleanup] Error processing call ${call.call_id}:`, error.message);
          errorCount++;
        }
      }
      
      // Log cleanup summary
      if (cleanedCount > 0 || errorCount > 0) {
        console.log(`[VideoSDKSessionCleanup] Cleanup cycle completed: ${cleanedCount} rooms cleaned, ${errorCount} errors`);
      }
      
      // Also cleanup old ended calls that might still have active VideoSDK rooms
      await this.cleanupOldEndedCalls();
      
    } catch (error) {
      console.error('[VideoSDKSessionCleanup] Cleanup cycle failed:', error);
    }
  }

  /**
   * Update call status after cleanup
   */
  async updateCallStatusAfterCleanup(callId, newStatus) {
    try {
      // Update call status without cleanup_reason column (doesn't exist in current schema)
      const updateQuery = `
        UPDATE user_calls
        SET call_status = ?, end_time = NOW()
        WHERE call_id = ?
      `;

      await queryRunner(updateQuery, [newStatus, callId]);

      // TODO: Log the cleanup action when call_logs table is created
      // const logQuery = `
      //   INSERT INTO call_logs (call_id, action, timestamp, details)
      //   VALUES (?, 'auto_cleanup', NOW(), ?)
      // `;
      //
      // const logDetails = JSON.stringify({
      //   reason: 'empty_room_auto_cleanup',
      //   cleanup_service: 'VideoSDKSessionCleanup'
      // });
      //
      // await queryRunner(logQuery, [callId, logDetails]);

      console.log(`[VideoSDKSessionCleanup] Updated call ${callId} status to ${newStatus} after cleanup`);

    } catch (error) {
      console.error(`[VideoSDKSessionCleanup] Failed to update call ${callId} after cleanup:`, error);
    }
  }

  /**
   * Cleanup old ended calls that might still have active VideoSDK rooms
   */
  async cleanupOldEndedCalls() {
    try {
      // Get calls that ended more than 5 minutes ago but might still have active rooms
      const oldEndedCallsQuery = `
        SELECT call_id, meeting_id, videosdk_token
        FROM user_calls 
        WHERE call_status IN ('completed', 'declined', 'missed', 'ended_for_new_call')
        AND meeting_id IS NOT NULL 
        AND meeting_id != ''
        AND end_time < DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        AND (cleanup_reason IS NULL OR cleanup_reason = '')
        LIMIT 10
      `;
      
      const oldCalls = await queryRunner(oldEndedCallsQuery);
      
      if (oldCalls.length > 0) {
        console.log(`[VideoSDKSessionCleanup] Found ${oldCalls.length} old ended calls to cleanup`);
        
        for (const call of oldCalls) {
          try {
            await VideoSDKService.deactivateRoom(call.meeting_id, call.videosdk_token);
            
            // Mark as cleaned up
            const markCleanedQuery = `
              UPDATE user_calls 
              SET cleanup_reason = 'auto_old_call_cleanup'
              WHERE call_id = ?
            `;
            await queryRunner(markCleanedQuery, [call.call_id]);
            
            console.log(`[VideoSDKSessionCleanup] Cleaned up old call room: ${call.meeting_id}`);
            
          } catch (error) {
            console.warn(`[VideoSDKSessionCleanup] Failed to cleanup old call ${call.call_id}:`, error.message);
          }
        }
      }
      
    } catch (error) {
      console.error('[VideoSDKSessionCleanup] Failed to cleanup old ended calls:', error);
    }
  }

  /**
   * Get cleanup service status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      cleanupIntervalMs: this.cleanupIntervalMs,
      emptyRoomThresholdMs: this.emptyRoomThresholdMs,
      nextCleanupIn: this.isRunning ? this.cleanupIntervalMs : null
    };
  }
}

// Create singleton instance
const sessionCleanupService = new VideoSDKSessionCleanup();

module.exports = sessionCleanupService;
